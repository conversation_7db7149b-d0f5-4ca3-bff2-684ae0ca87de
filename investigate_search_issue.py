#!/usr/bin/env python3
"""
Comprehensive investigation of the search issue
"""

import requests
from bs4 import BeautifulSoup
import json

def test_actual_search_behavior():
    """Test what's really happening with the search"""
    print("🔍 Comprehensive Search Investigation")
    print("=" * 60)
    
    base_url = "https://forum.rigsofrods.org"
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # Test multiple very different search terms
    test_queries = [
        '',  # No search (baseline)
        'BMW',
        'Ford', 
        'airplane',
        'boat',
        'train',
        'monster',
        'fire',
        'police',
        'school',
        'nonexistentterm12345'  # Should return no results
    ]
    
    results = {}
    
    for query in test_queries:
        print(f"\n📡 Testing query: '{query}'")
        
        if query:
            params = {
                'keywords': query,
                'order': 'relevance',
                'direction': 'desc'
            }
        else:
            params = {
                'order': 'download_count',
                'direction': 'desc'
            }
        
        try:
            response = session.get(f"{base_url}/resources/", params=params, timeout=30)
            print(f"   URL: {response.url}")
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Extract all resource titles and IDs
                resource_items = soup.find_all('div', class_='structItem')
                titles = []
                
                for item in resource_items:
                    title_div = item.find('div', class_='structItem-title')
                    if title_div:
                        title_link = title_div.find('a')
                        if title_link:
                            title = title_link.get_text(strip=True)
                            href = title_link.get('href', '')
                            titles.append(title)
                
                results[query] = titles
                print(f"   Found {len(titles)} resources")
                
                # Show first 5 titles
                for i, title in enumerate(titles[:5]):
                    print(f"     {i+1}. {title}")
                
                # Check if query appears in any title
                if query:
                    matches = [title for title in titles if query.lower() in title.lower()]
                    print(f"   Direct matches: {len(matches)}")
                    for match in matches:
                        print(f"     ✓ {match}")
        
        except Exception as e:
            print(f"   Error: {e}")
    
    # Compare all results
    print(f"\n📊 Results Comparison:")
    print("=" * 60)
    
    baseline = results.get('', [])
    
    for query, titles in results.items():
        if query == '':
            continue
        
        # Check if results are identical to baseline or no-search
        identical_to_baseline = titles == baseline
        
        # Check if results are identical to other searches
        identical_to_others = []
        for other_query, other_titles in results.items():
            if other_query != query and other_query != '' and titles == other_titles:
                identical_to_others.append(other_query)
        
        print(f"\nQuery: '{query}'")
        print(f"  Results count: {len(titles)}")
        print(f"  Identical to baseline (no search): {identical_to_baseline}")
        if identical_to_others:
            print(f"  Identical to other searches: {identical_to_others}")
        
        # Check for actual matches
        if query:
            matches = [title for title in titles if query.lower() in title.lower()]
            print(f"  Direct title matches: {len(matches)}")

def test_different_endpoints():
    """Test if there are different endpoints that work better"""
    print(f"\n🎯 Testing Different Endpoints")
    print("=" * 60)
    
    base_url = "https://forum.rigsofrods.org"
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    # Test different endpoints and parameters
    test_cases = [
        {
            'name': 'Resources with keywords',
            'url': f"{base_url}/resources/",
            'params': {'keywords': 'BMW'}
        },
        {
            'name': 'Resources with q parameter',
            'url': f"{base_url}/resources/",
            'params': {'q': 'BMW'}
        },
        {
            'name': 'Resources with search parameter',
            'url': f"{base_url}/resources/",
            'params': {'search': 'BMW'}
        },
        {
            'name': 'Find new resources',
            'url': f"{base_url}/find-new/resources",
            'params': {'keywords': 'BMW'}
        },
        {
            'name': 'Search endpoint',
            'url': f"{base_url}/search/",
            'params': {'q': 'BMW', 'type': 'resource'}
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📡 Testing: {test_case['name']}")
        
        try:
            response = session.get(test_case['url'], params=test_case['params'], timeout=30)
            print(f"   Final URL: {response.url}")
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Look for different types of results
                resource_items = soup.find_all('div', class_='structItem')
                content_rows = soup.find_all('div', class_='contentRow')
                
                print(f"   structItem elements: {len(resource_items)}")
                print(f"   contentRow elements: {len(content_rows)}")
                
                # Check for BMW matches in both types
                bmw_matches = 0
                all_items = resource_items + content_rows
                
                for item in all_items:
                    # Try different title selectors
                    title_elem = (item.find('div', class_='structItem-title') or 
                                item.find('h3', class_='contentRow-title') or
                                item.find('a', class_='fauxBlockLink-blockLink'))
                    
                    if title_elem:
                        title_link = title_elem.find('a') if title_elem.name != 'a' else title_elem
                        if title_link:
                            title = title_link.get_text(strip=True)
                            if 'bmw' in title.lower():
                                bmw_matches += 1
                                print(f"     ✓ BMW match: {title}")
                
                print(f"   BMW matches found: {bmw_matches}")
        
        except Exception as e:
            print(f"   Error: {e}")

def test_manual_search_form():
    """Test submitting search through the actual form"""
    print(f"\n📝 Testing Manual Search Form Submission")
    print("=" * 60)
    
    base_url = "https://forum.rigsofrods.org"
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    try:
        # First get the search page to extract form details
        search_page = session.get(f"{base_url}/search/", timeout=30)
        soup = BeautifulSoup(search_page.content, 'html.parser')
        
        # Find the search form
        search_form = soup.find('form', action='/search/search')
        if search_form:
            print("Found search form")
            
            # Extract CSRF token
            csrf_token = None
            token_input = search_form.find('input', {'name': '_xfToken'})
            if token_input:
                csrf_token = token_input.get('value')
                print(f"CSRF token: {csrf_token[:20]}...")
            
            # Submit search for BMW in resources
            search_data = {
                'keywords': 'BMW',
                'c[nodes][0]': '2',  # Resources node
                'c[child_nodes]': '1',
                'c[title_only]': '0',
                'order': 'relevance'
            }
            
            if csrf_token:
                search_data['_xfToken'] = csrf_token
            
            print("Submitting search form...")
            search_response = session.post(
                f"{base_url}/search/search",
                data=search_data,
                timeout=30,
                allow_redirects=True
            )
            
            print(f"Search result URL: {search_response.url}")
            print(f"Status: {search_response.status_code}")
            
            if search_response.status_code == 200:
                result_soup = BeautifulSoup(search_response.content, 'html.parser')
                
                # Look for search results
                search_results = result_soup.find_all('div', class_='contentRow')
                print(f"Search results found: {len(search_results)}")
                
                bmw_matches = 0
                for result in search_results:
                    title_elem = result.find('a', class_='fauxBlockLink-blockLink')
                    if title_elem:
                        title = title_elem.get_text(strip=True)
                        href = title_elem.get('href', '')
                        
                        if 'bmw' in title.lower():
                            bmw_matches += 1
                            print(f"  ✓ BMW match: {title}")
                        
                        # Check if it's a resource
                        if '/resources/' in href:
                            print(f"  Resource: {title}")
                
                print(f"Total BMW matches: {bmw_matches}")
        
        else:
            print("Search form not found")
    
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_actual_search_behavior()
    test_different_endpoints()
    test_manual_search_form()
    
    print(f"\n✅ Investigation complete!")
