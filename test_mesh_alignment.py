#!/usr/bin/env python3
"""
Test script to verify mesh alignment fixes in truck2jbeam converter
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from truck2jbeam import convert_single_file, ConversionConfig
import logging

def setup_logging():
    """Setup logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )

def create_test_ror_file():
    """Create a test RoR file with flexbodies and props for testing coordinate transformation"""
    test_content = """
; Test RoR file for mesh alignment verification
; This file contains flexbodies and props to test coordinate system conversion

nodes
; id, x, y, z
0, 0.0, 0.0, 0.0
1, 1.0, 0.0, 0.0
2, 0.0, 1.0, 0.0
3, 0.0, 0.0, 1.0
4, 1.0, 1.0, 0.0
5, 1.0, 0.0, 1.0
6, 0.0, 1.0, 1.0
7, 1.0, 1.0, 1.0

beams
; id1, id2
0, 1
0, 2
0, 3
1, 4
1, 5
2, 4
2, 6
3, 5
3, 6
4, 7
5, 7
6, 7

flexbodies
; ref, x, y, offsetx, offsety, offsetz, rotx, roty, rotz, mesh
0, 1, 2, 0.5, 0.5, 0.5, 0, 0, 0, test_body.mesh
1, 4, 5, 0.0, 1.0, 0.0, 90, 45, 30, test_body2.mesh

props
; ref, x, y, offsetx, offsety, offsetz, rotx, roty, rotz, mesh
3, 5, 6, 0.0, 0.0, 0.5, 45, 90, 60, test_prop.mesh
"""
    return test_content

def test_coordinate_transformation():
    """Test the coordinate transformation fixes"""
    print("🧪 Testing Mesh Alignment Coordinate Transformation")
    print("=" * 60)
    
    # Create temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create test RoR file
        test_file = temp_path / "test_vehicle.truck"
        with open(test_file, 'w') as f:
            f.write(create_test_ror_file())
        
        print(f"📁 Created test file: {test_file}")
        
        # Create output directory
        output_dir = temp_path / "output"
        output_dir.mkdir()
        
        # Create conversion config
        config = ConversionConfig(
            output_dir=str(output_dir),
            convert_meshes=False,  # Skip mesh conversion for this test
            mesh_output_format="dae",
            mesh_output_dir=str(output_dir / "meshes"),
            no_transform_properties=False,
            verbose=True
        )
        
        # Create logger
        logger = logging.getLogger(__name__)
        
        print(f"🔄 Converting with coordinate transformation...")
        
        try:
            # Perform conversion
            result = convert_single_file(str(test_file), config, logger)
            
            if result:
                print(f"✅ Conversion successful!")
                
                # Read and analyze the output JBeam file
                jbeam_file = output_dir / "test_vehicle.jbeam"
                if jbeam_file.exists():
                    print(f"📄 Analyzing output JBeam file...")
                    analyze_jbeam_coordinates(jbeam_file)
                else:
                    print(f"❌ JBeam file not found: {jbeam_file}")
            else:
                print(f"❌ Conversion failed!")
                
        except Exception as e:
            print(f"❌ Conversion error: {e}")
            import traceback
            traceback.print_exc()

def analyze_jbeam_coordinates(jbeam_file):
    """Analyze the JBeam file to verify coordinate transformation"""
    print(f"\n📊 Coordinate Transformation Analysis")
    print("=" * 50)
    
    try:
        with open(jbeam_file, 'r') as f:
            content = f.read()
        
        print(f"📋 JBeam file content preview:")
        print("-" * 30)
        
        # Extract and display key sections
        lines = content.split('\n')
        in_nodes = False
        in_flexbodies = False
        in_props = False
        
        node_count = 0
        flexbody_count = 0
        prop_count = 0
        
        for line in lines:
            line = line.strip()
            
            # Track sections
            if '"nodes":[' in line:
                in_nodes = True
                print(f"\n🔹 NODES SECTION:")
                continue
            elif '"flexbodies":[' in line:
                in_nodes = False
                in_flexbodies = True
                print(f"\n🔹 FLEXBODIES SECTION:")
                continue
            elif '"props":[' in line:
                in_flexbodies = False
                in_props = True
                print(f"\n🔹 PROPS SECTION:")
                continue
            elif line.startswith(']') and (in_nodes or in_flexbodies or in_props):
                in_nodes = in_flexbodies = in_props = False
                continue
            
            # Display relevant lines
            if in_nodes and line.startswith('["') and node_count < 8:
                print(f"   Node: {line}")
                node_count += 1
                
                # Analyze coordinate transformation
                if node_count <= 4:  # Analyze first few nodes
                    analyze_node_coordinates(line, node_count - 1)
            
            elif in_flexbodies and line.startswith('["') and flexbody_count < 5:
                print(f"   Flexbody: {line}")
                flexbody_count += 1
                
                # Analyze flexbody positioning
                analyze_flexbody_positioning(line, flexbody_count - 1)
            
            elif in_props and line.startswith('["') and prop_count < 5:
                print(f"   Prop: {line}")
                prop_count += 1
                
                # Analyze prop positioning
                analyze_prop_positioning(line, prop_count - 1)
        
        print(f"\n📈 Summary:")
        print(f"   Nodes processed: {node_count}")
        print(f"   Flexbodies processed: {flexbody_count}")
        print(f"   Props processed: {prop_count}")
        
    except Exception as e:
        print(f"❌ Error analyzing JBeam file: {e}")

def analyze_node_coordinates(line, node_index):
    """Analyze node coordinate transformation"""
    try:
        # Parse node line: ["node_name", x, y, z]
        import json
        parts = line.rstrip(',').split(', ')
        if len(parts) >= 4:
            name = parts[0].strip('["')
            x = float(parts[1])
            y = float(parts[2])
            z = float(parts[3].rstrip(']'))
            
            # Expected original RoR coordinates for test nodes
            original_coords = [
                (0.0, 0.0, 0.0),  # Node 0
                (1.0, 0.0, 0.0),  # Node 1
                (0.0, 1.0, 0.0),  # Node 2
                (0.0, 0.0, 1.0),  # Node 3
            ]
            
            if node_index < len(original_coords):
                orig_x, orig_y, orig_z = original_coords[node_index]
                
                print(f"     Original RoR: ({orig_x}, {orig_y}, {orig_z})")
                print(f"     BeamNG Output: ({x}, {y}, {z})")
                
                # Verify transformation: RoR(X,Y,Z) -> BeamNG(X,Z,Y)
                expected_x = orig_x  # X stays same
                expected_y = orig_z  # RoR Z -> BeamNG Y
                expected_z = orig_y  # RoR Y -> BeamNG Z
                
                if abs(x - expected_x) < 0.001 and abs(y - expected_y) < 0.001 and abs(z - expected_z) < 0.001:
                    print(f"     ✅ Coordinate transformation correct!")
                else:
                    print(f"     ❌ Coordinate transformation incorrect!")
                    print(f"     Expected: ({expected_x}, {expected_y}, {expected_z})")
                
    except Exception as e:
        print(f"     ❌ Error parsing node: {e}")

def analyze_flexbody_positioning(line, flexbody_index):
    """Analyze flexbody positioning transformation"""
    try:
        # Look for position data in flexbody line
        if '"pos":' in line:
            # Extract position data
            pos_start = line.find('"pos":') + 6
            pos_end = line.find('}', pos_start) + 1
            pos_data = line[pos_start:pos_end]
            
            print(f"     Position data: {pos_data}")
            
            # Expected transformations for test flexbodies
            expected_data = [
                {"offsets": (0.5, 0.5, 0.5), "rotations": (0, 0, 0)},      # First flexbody
                {"offsets": (0.0, 1.0, 0.0), "rotations": (90, 45, 30)},   # Second flexbody
            ]
            
            if flexbody_index < len(expected_data):
                data = expected_data[flexbody_index]
                orig_x, orig_y, orig_z = data["offsets"]
                orig_rot_x, orig_rot_y, orig_rot_z = data["rotations"]

                print(f"     Original RoR offsets: ({orig_x}, {orig_y}, {orig_z})")
                print(f"     Original RoR rotations: ({orig_rot_x}, {orig_rot_y}, {orig_rot_z})")

                # Expected BeamNG transformation: (X, Z, Y)
                expected_x = orig_x  # X stays same
                expected_y = orig_z  # RoR Z -> BeamNG Y
                expected_z = orig_y  # RoR Y -> BeamNG Z

                # Expected rotation corrections
                expected_rot_x = ((orig_rot_x + 90 + 180) % 360) - 180  # X + 90 degrees
                expected_rot_y = ((orig_rot_z + 180) % 360) - 180       # RoR Z -> BeamNG Y
                expected_rot_z = ((orig_rot_y + 180 + 180) % 360) - 180 # RoR Y -> BeamNG Z + 180

                print(f"     Expected BeamNG offsets: ({expected_x}, {expected_y}, {expected_z})")
                print(f"     Expected BeamNG rotations: ({expected_rot_x}, {expected_rot_y}, {expected_rot_z})")

                # Look for rotation data in the line
                if '"rot":' in line:
                    rot_start = line.find('"rot":') + 6
                    rot_end = line.find('}', rot_start) + 1
                    rot_data = line[rot_start:rot_end]
                    print(f"     Actual rotation data: {rot_data}")
                
    except Exception as e:
        print(f"     ❌ Error parsing flexbody: {e}")

def analyze_prop_positioning(line, prop_index):
    """Analyze prop positioning transformation"""
    try:
        # Look for rotation data in prop line
        if '"baseRotation":' in line:
            # Extract rotation data
            rot_start = line.find('"baseRotation":') + 15
            rot_end = line.find('}', rot_start) + 1
            rot_data = line[rot_start:rot_end]
            
            print(f"     Rotation data: {rot_data}")
            
            # Expected rotation transformations for test props
            expected_prop_rotations = [
                (45, 90, 60),  # First prop: RoR rotations (X, Y, Z)
            ]

            if prop_index < len(expected_prop_rotations):
                orig_x, orig_y, orig_z = expected_prop_rotations[prop_index]

                print(f"     Original RoR rotations: ({orig_x}, {orig_y}, {orig_z})")

                # Expected rotation corrections with coordinate system transformation
                expected_x = ((orig_x + 90 + 180) % 360) - 180  # X + 90 degrees
                expected_y = ((orig_z + 180) % 360) - 180       # RoR Z -> BeamNG Y
                expected_z = ((orig_y + 180 + 180) % 360) - 180 # RoR Y -> BeamNG Z + 180

                print(f"     Expected BeamNG rotations: ({expected_x}, {expected_y}, {expected_z})")

                # Parse actual rotation values from the line
                try:
                    import json
                    import re

                    # Extract the rotation dictionary from the line
                    rot_match = re.search(r'"baseRotation":\s*({[^}]+})', line)
                    if rot_match:
                        rot_str = rot_match.group(1)
                        rot_dict = json.loads(rot_str)
                        actual_x = rot_dict.get('x', 0)
                        actual_y = rot_dict.get('y', 0)
                        actual_z = rot_dict.get('z', 0)

                        print(f"     Actual BeamNG rotations: ({actual_x}, {actual_y}, {actual_z})")

                        # Check if rotations are correct (within tolerance)
                        tolerance = 1.0  # 1 degree tolerance
                        x_correct = abs(actual_x - expected_x) < tolerance
                        y_correct = abs(actual_y - expected_y) < tolerance
                        z_correct = abs(actual_z - expected_z) < tolerance

                        if x_correct and y_correct and z_correct:
                            print(f"     ✅ Rotation transformation correct!")
                        else:
                            print(f"     ❌ Rotation transformation incorrect!")
                            if not x_correct:
                                print(f"       X-axis: expected {expected_x}, got {actual_x}")
                            if not y_correct:
                                print(f"       Y-axis: expected {expected_y}, got {actual_y}")
                            if not z_correct:
                                print(f"       Z-axis: expected {expected_z}, got {actual_z}")

                except Exception as e:
                    print(f"     ❌ Error parsing rotation values: {e}")
                
    except Exception as e:
        print(f"     ❌ Error parsing prop: {e}")

def main():
    """Run mesh alignment tests"""
    setup_logging()
    
    print("🔧 Mesh Alignment Fix Verification")
    print("=" * 70)
    print("This test verifies that the coordinate system conversion fixes")
    print("properly transform RoR coordinates to BeamNG coordinates.")
    print()
    
    test_coordinate_transformation()
    
    print(f"\n✅ Testing complete!")
    print(f"\n📋 Key Fixes Implemented:")
    print(f"   🔹 Node coordinates: RoR(X,Y,Z) -> BeamNG(X,Z,Y)")
    print(f"   🔹 Flexbody offsets: RoR(X,Y,Z) -> BeamNG(X,Z,Y)")
    print(f"   🔹 Mesh vertex coordinates: RoR(X,Y,Z) -> BeamNG(X,Z,Y)")
    print(f"   🔹 Rotation mapping: RoR(X,Y,Z) -> BeamNG(X,Z,Y)")
    print(f"   🔹 Consistent coordinate system throughout conversion pipeline")

if __name__ == "__main__":
    main()
