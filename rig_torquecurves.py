# default Rigs of Rods torquecurves

default = [[0,1.0],
          [1000,1.0],
          [10000,1.0]]

diesel = [[0,0],
         [1000,0.79],
         [1500,0.9],
         [2000,0.97],
         [2500,0.99],
         [3000,0.9],
         [3500,0.77]]


turbodiesel = [[0,0],
              [1000,0.89],
              [1500,1],
              [2000,1],
              [2500,1],
              [3000,1],
              [3500,1],
              [4000,0.89],
              [4500,0.81],
              [5000,0.65]]

gas = [[0,0],
      [1000,0.75],
      [1500,0.8],
      [2000,0.88],
      [2500,0.93],
      [3000,1.0],
      [3500,0.98],
      [4000,0.93],
      [4500,0.9],
      [5000,0.88],
      [5500,0.83],
      [6000,0.78]]


turbogas = [[0,0],
           [1000,0.67],
           [1500,1],
           [2000,1],
           [2500,1],
           [3000,1],
           [3500,1],
           [4000,1],
           [4500,1],
           [5000,0.95],
           [5500,0.88],
           [6000,0.83]]


wheelloader = [[0,0],
              [800,0.66],
              [900,0.74],
              [1000,0.81],
              [1100,0.88],
              [1200,0.96],
              [1300,0.99],
              [1400,1.0],
              [1500,0.93],
              [1600,0.88],
              [1700,0.85],
              [1800,0.77],
              [1900,0.74],
              [2000,0.7],
              [2100,0.66],
              [2200,0.63],
              [2300,0.59]]

compacttractor = [[1800,0.87],
                 [2000,0.87],
                 [2200,0.84],
                 [2400,0.85],
                 [2600,0.88],
                 [2800,0.88],
                 [3000,0.89],
                 [3200,0.93],
                 [3400,0.96],
                 [3600,1.0]]

tractor = [[1000,0.9],
          [1100,0.93],
          [1200,0.98],
          [1300,0.99],
          [1400,1.0],
          [1500,1.0],
          [1600,0.97],
          [1700,0.93],
          [1800,0.87],
          [1900,0.83],
          [2000,0.77],
          [2100,0.67],
          [2200,0.5]]


hydrostatic = [[400,1.0],
              [600,0.7],
              [800,0.55],
              [1000,0.4],
              [1200,0.35],
              [1400,0.3],
              [1600,0.25]]
 
# lazy, but works 
def get_curve(curve):
  if curve == "default":
    return default
  elif curve == "diesel":
    return diesel
  elif curve == "turbodiesel":
    return turbodiesel
  elif curve == "gas":
    return gas
  elif curve == "turbogas":
    return turbogas
  elif curve == "wheelloader":
    return wheelloader
  elif curve == "compacttractor":
    return compacttractor
  elif curve == "tractor":
    return tractor
  elif curve == "hydrostatic":
    return hydrostatic
  else:
    # not found
    return default