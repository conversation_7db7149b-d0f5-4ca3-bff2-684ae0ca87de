#!/usr/bin/env python3
"""
Verify rotation fix calculations for mesh alignment
"""

def normalize_angle(angle):
    """Normalize angle to -180 to 180 range"""
    return ((angle + 180) % 360) - 180

def test_rotation_corrections():
    """Test the rotation correction formulas"""
    print("🔄 Rotation Correction Verification")
    print("=" * 50)
    
    # Test cases from our RoR file
    test_cases = [
        {"name": "Flexbody 1", "ror": (0, 0, 0), "description": "No rotation"},
        {"name": "Flexbody 2", "ror": (90, 45, 30), "description": "Complex rotation"},
        {"name": "Prop 1", "ror": (45, 90, 60), "description": "Prop rotation"},
    ]
    
    for case in test_cases:
        print(f"\n🔹 {case['name']} - {case['description']}")
        ror_x, ror_y, ror_z = case["ror"]
        
        print(f"   Original RoR: X={ror_x}°, Y={ror_y}°, Z={ror_z}°")
        
        # Apply the rotation corrections from our fix
        # X-axis rotation (Roll): RoR X-rotation + 90 degree correction
        beamng_x = normalize_angle(ror_x + 90.0)
        
        # Y-axis rotation (Pitch): RoR Z-rotation becomes BeamNG Y-rotation  
        beamng_y = normalize_angle(ror_z)
        
        # Z-axis rotation (Yaw): RoR Y-rotation becomes BeamNG Z-rotation + 180 degree correction
        beamng_z = normalize_angle(ror_y + 180.0)
        
        print(f"   Converted BeamNG: X={beamng_x}°, Y={beamng_y}°, Z={beamng_z}°")
        
        # Show the transformation breakdown
        print(f"   Transformation breakdown:")
        print(f"     X: {ror_x}° + 90° = {ror_x + 90}° → {beamng_x}° (normalized)")
        print(f"     Y: RoR Z({ror_z}°) → BeamNG Y({beamng_y}°)")
        print(f"     Z: {ror_y}° + 180° = {ror_y + 180}° → {beamng_z}° (normalized)")

def verify_test_output():
    """Verify against the actual test output"""
    print(f"\n📊 Verification Against Test Output")
    print("=" * 50)
    
    # From the test output
    actual_results = [
        {"name": "Flexbody 1", "actual": (90.0, 0.0, -180.0), "ror": (0, 0, 0)},
        {"name": "Flexbody 2", "actual": (120.0, 45.0, -90.0), "ror": (90, 45, 30)},
        {"name": "Prop 1", "actual": (150.0, 90.0, -135.0), "ror": (45, 90, 60)},
    ]
    
    for result in actual_results:
        print(f"\n🔹 {result['name']}")
        ror_x, ror_y, ror_z = result["ror"]
        actual_x, actual_y, actual_z = result["actual"]
        
        # Calculate expected values
        expected_x = normalize_angle(ror_x + 90.0)
        expected_y = normalize_angle(ror_z)
        expected_z = normalize_angle(ror_y + 180.0)
        
        print(f"   RoR input: ({ror_x}, {ror_y}, {ror_z})")
        print(f"   Expected:  ({expected_x}, {expected_y}, {expected_z})")
        print(f"   Actual:    ({actual_x}, {actual_y}, {actual_z})")
        
        # Check if they match (within tolerance)
        tolerance = 1.0
        x_match = abs(actual_x - expected_x) < tolerance
        y_match = abs(actual_y - expected_y) < tolerance
        z_match = abs(actual_z - expected_z) < tolerance
        
        if x_match and y_match and z_match:
            print(f"   ✅ Perfect match!")
        else:
            print(f"   ⚠️  Differences found:")
            if not x_match:
                print(f"      X: expected {expected_x}°, got {actual_x}° (diff: {actual_x - expected_x}°)")
            if not y_match:
                print(f"      Y: expected {expected_y}°, got {actual_y}° (diff: {actual_y - expected_y}°)")
            if not z_match:
                print(f"      Z: expected {expected_z}°, got {actual_z}° (diff: {actual_z - expected_z}°)")

def explain_coordinate_system():
    """Explain the coordinate system transformation"""
    print(f"\n📐 Coordinate System Transformation Explanation")
    print("=" * 60)
    
    print(f"RoR Coordinate System:")
    print(f"  X-axis: Right")
    print(f"  Y-axis: Up")
    print(f"  Z-axis: Forward")
    
    print(f"\nBeamNG Coordinate System:")
    print(f"  X-axis: Right")
    print(f"  Y-axis: Forward")
    print(f"  Z-axis: Up")
    
    print(f"\nRotation Mapping:")
    print(f"  RoR X-rotation (Roll around right axis) → BeamNG X-rotation + 90° correction")
    print(f"  RoR Y-rotation (Pitch around up axis) → BeamNG Z-rotation + 180° correction")
    print(f"  RoR Z-rotation (Yaw around forward axis) → BeamNG Y-rotation")
    
    print(f"\nWhy the corrections are needed:")
    print(f"  • 90° X-axis correction: Accounts for different mesh orientation conventions")
    print(f"  • 180° Z-axis correction: Accounts for different yaw direction conventions")
    print(f"  • Y-axis mapping: Direct mapping from RoR Z to BeamNG Y (forward axis)")

def main():
    """Main verification function"""
    print("🔧 Mesh Alignment Rotation Fix Verification")
    print("=" * 70)
    
    test_rotation_corrections()
    verify_test_output()
    explain_coordinate_system()
    
    print(f"\n✅ Verification Summary:")
    print(f"   🔹 X-axis rotation: +90° correction applied")
    print(f"   🔹 Y-axis rotation: RoR Z → BeamNG Y mapping")
    print(f"   🔹 Z-axis rotation: +180° correction applied")
    print(f"   🔹 All rotations normalized to -180° to +180° range")
    print(f"   🔹 Fixes address the reported 90° X-axis and 180° Z-axis offsets")

if __name__ == "__main__":
    main()
