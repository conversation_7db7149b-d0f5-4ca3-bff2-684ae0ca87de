#!/usr/bin/env python3
"""
Simple test to verify rotation corrections are working
"""

import os
import sys
import tempfile
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from truck2jbeam import convert_single_file, ConversionConfig
import logging

def test_rotation_fix():
    """Test the rotation fix with specific known values"""
    print("🔄 Testing Rotation Fix")
    print("=" * 40)
    
    # Create test RoR file with known rotation values
    test_content = """
; Test rotation fix
nodes
0, 0.0, 0.0, 0.0
1, 1.0, 0.0, 0.0
2, 0.0, 1.0, 0.0

beams
0, 1
0, 2
1, 2

flexbodies
; Test case: X=0, Y=0, Z=0 (should become X=90, Y=0, Z=180->-180)
0, 1, 2, 0.0, 0.0, 0.0, 0, 0, 0, test1.mesh

; Test case: X=45, Y=90, Z=60 (should become X=135, Y=60, Z=270->-90)
0, 1, 2, 0.0, 0.0, 0.0, 45, 90, 60, test2.mesh

props
; Test case: X=30, Y=45, Z=90 (should become X=120, Y=90, Z=225->-135)
0, 1, 2, 0.0, 0.0, 0.0, 30, 45, 90, test3.mesh
"""
    
    # Create temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create test RoR file
        test_file = temp_path / "rotation_test.truck"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        # Create output directory
        output_dir = temp_path / "output"
        output_dir.mkdir()
        
        # Create conversion config
        config = ConversionConfig(
            output_dir=str(output_dir),
            convert_meshes=False,
            mesh_output_format="dae",
            mesh_output_dir=str(output_dir / "meshes"),
            no_transform_properties=False,
            verbose=False
        )
        
        # Create logger
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.WARNING)  # Reduce log noise
        
        print(f"Converting test file...")
        
        try:
            # Perform conversion
            result = convert_single_file(str(test_file), config, logger)
            
            if result:
                print(f"✅ Conversion successful!")
                
                # Read and analyze the output JBeam file
                jbeam_file = output_dir / "rotation_test.jbeam"
                if jbeam_file.exists():
                    analyze_rotation_output(jbeam_file)
                else:
                    print(f"❌ JBeam file not found")
            else:
                print(f"❌ Conversion failed!")
                
        except Exception as e:
            print(f"❌ Conversion error: {e}")

def analyze_rotation_output(jbeam_file):
    """Analyze the rotation output"""
    print(f"\n📊 Rotation Analysis")
    print("=" * 30)
    
    try:
        with open(jbeam_file, 'r') as f:
            content = f.read()
        
        # Test cases with expected results
        test_cases = [
            {"name": "Test 1", "input": (0, 0, 0), "expected": (90, 0, -180)},
            {"name": "Test 2", "input": (45, 90, 60), "expected": (135, 60, -90)},
            {"name": "Test 3 (Prop)", "input": (30, 45, 90), "expected": (120, 90, -135)},
        ]
        
        lines = content.split('\n')
        flexbody_count = 0
        prop_count = 0
        
        for line in lines:
            line = line.strip()
            
            # Check flexbody rotations
            if '"rot":' in line and flexbody_count < 2:
                print(f"\n🔹 {test_cases[flexbody_count]['name']} (Flexbody)")
                input_rot = test_cases[flexbody_count]['input']
                expected_rot = test_cases[flexbody_count]['expected']
                
                print(f"   Input RoR: X={input_rot[0]}°, Y={input_rot[1]}°, Z={input_rot[2]}°")
                print(f"   Expected:  X={expected_rot[0]}°, Y={expected_rot[1]}°, Z={expected_rot[2]}°")
                
                # Extract actual rotation values
                import re
                rot_match = re.search(r'"rot":\s*\{[^}]+\}', line)
                if rot_match:
                    rot_str = rot_match.group(0)
                    print(f"   Actual:    {rot_str}")
                    
                    # Parse values
                    x_match = re.search(r'"x":\s*([+-]?\d*\.?\d+)', rot_str)
                    y_match = re.search(r'"y":\s*([+-]?\d*\.?\d+)', rot_str)
                    z_match = re.search(r'"z":\s*([+-]?\d*\.?\d+)', rot_str)
                    
                    if x_match and y_match and z_match:
                        actual_x = float(x_match.group(1))
                        actual_y = float(y_match.group(1))
                        actual_z = float(z_match.group(1))
                        
                        print(f"   Parsed:    X={actual_x}°, Y={actual_y}°, Z={actual_z}°")
                        
                        # Check if correct (within tolerance)
                        tolerance = 1.0
                        x_correct = abs(actual_x - expected_rot[0]) < tolerance
                        y_correct = abs(actual_y - expected_rot[1]) < tolerance
                        z_correct = abs(actual_z - expected_rot[2]) < tolerance
                        
                        if x_correct and y_correct and z_correct:
                            print(f"   ✅ Rotation fix working correctly!")
                        else:
                            print(f"   ❌ Rotation fix not working:")
                            if not x_correct:
                                print(f"      X: expected {expected_rot[0]}°, got {actual_x}°")
                            if not y_correct:
                                print(f"      Y: expected {expected_rot[1]}°, got {actual_y}°")
                            if not z_correct:
                                print(f"      Z: expected {expected_rot[2]}°, got {actual_z}°")
                
                flexbody_count += 1
            
            # Check prop rotations
            elif '"baseRotation":' in line and prop_count < 1:
                print(f"\n🔹 {test_cases[2]['name']} (Prop)")
                input_rot = test_cases[2]['input']
                expected_rot = test_cases[2]['expected']
                
                print(f"   Input RoR: X={input_rot[0]}°, Y={input_rot[1]}°, Z={input_rot[2]}°")
                print(f"   Expected:  X={expected_rot[0]}°, Y={expected_rot[1]}°, Z={expected_rot[2]}°")
                
                # Extract actual rotation values
                import re
                rot_match = re.search(r'"baseRotation":\s*\{[^}]+\}', line)
                if rot_match:
                    rot_str = rot_match.group(0)
                    print(f"   Actual:    {rot_str}")
                    
                    # Parse values
                    x_match = re.search(r'"x":\s*([+-]?\d*\.?\d+)', rot_str)
                    y_match = re.search(r'"y":\s*([+-]?\d*\.?\d+)', rot_str)
                    z_match = re.search(r'"z":\s*([+-]?\d*\.?\d+)', rot_str)
                    
                    if x_match and y_match and z_match:
                        actual_x = float(x_match.group(1))
                        actual_y = float(y_match.group(1))
                        actual_z = float(z_match.group(1))
                        
                        print(f"   Parsed:    X={actual_x}°, Y={actual_y}°, Z={actual_z}°")
                        
                        # Check if correct (within tolerance)
                        tolerance = 1.0
                        x_correct = abs(actual_x - expected_rot[0]) < tolerance
                        y_correct = abs(actual_y - expected_rot[1]) < tolerance
                        z_correct = abs(actual_z - expected_rot[2]) < tolerance
                        
                        if x_correct and y_correct and z_correct:
                            print(f"   ✅ Rotation fix working correctly!")
                        else:
                            print(f"   ❌ Rotation fix not working:")
                            if not x_correct:
                                print(f"      X: expected {expected_rot[0]}°, got {actual_x}°")
                            if not y_correct:
                                print(f"      Y: expected {expected_rot[1]}°, got {actual_y}°")
                            if not z_correct:
                                print(f"      Z: expected {expected_rot[2]}°, got {actual_z}°")
                
                prop_count += 1
        
        print(f"\n📋 Summary:")
        print(f"   Flexbodies analyzed: {flexbody_count}")
        print(f"   Props analyzed: {prop_count}")
        
    except Exception as e:
        print(f"❌ Error analyzing output: {e}")

def main():
    """Main test function"""
    print("🔧 Rotation Fix Verification Test")
    print("=" * 50)
    print("Testing the 90° X-axis and 180° Z-axis rotation corrections")
    print()
    
    test_rotation_fix()
    
    print(f"\n✅ Test complete!")
    print(f"\nThe rotation fix addresses:")
    print(f"   • X-axis rotation: +90° correction")
    print(f"   • Z-axis rotation: +180° correction")
    print(f"   • Y-axis rotation: Direct RoR Z → BeamNG Y mapping")

if __name__ == "__main__":
    main()
